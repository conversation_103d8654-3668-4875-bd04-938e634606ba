# Rs ui react components

This is a react component library for the rs ui.

Its a packaged with [tsup](https://tsup.egoist.dev/#what-can-it-bundle)
and uses [vite](https://vite.dev/) as enginge

For displaying components use the [storybook](https://storybook.js.org/)

## Requirements

Need to have npm @fontsource/public-sans for font loading
and setup in project.

### Technologies

- [Node.js 22](https://nodejs.org/en)

- [tsup](https://tsup.egoist.dev/#what-can-it-bundle)

-  [vite](https://vite.dev/)

- [Tailwind CSS 4](https://tailwindcss.com/)

- [React 19](https://react.dev/)

### Testing

For unit testing vitest.
For e2e testing playwright.
For UX component testing storybook.
For linting Biome (and eslint)

- [Vitest](https://vitest.dev/)

- [Storybook](https://storybook.js.org/)

- [Biome](https://biomejs.dev/)

## Getting Started

First, run the development server:

```bash
pnpm storybook
```

Open [http://localhost:6006](http://localhost:6006) with your browser to see the result.


## Other Runners
There are also other runners for diffrent tasks.

- build , build the package
- lint , lint the code using biome
- test ,run unit tests using vitest
- test:coverage ,run unit tests coverage using vitest
- storybook , start the storybook UI/UX testing env
- build-storybook ,build a storybook (not needed)
- typecheck , typechecking, faster than build for checking for errors
- check ,lint and formatter checking
- check:fix ,lint and formatter checking but fixes unsafe checks

## Using the build component libary

For development usage.
first build the package
```
pnpm build
```

This will create and update the dist/ folder  with the build files.

Give other project access to it (the packagename is rs-ui-react-components *from package.json)
```
pnpm link
```

then in the other project add a link to the package
```
pnpm link rs-ui-react-components
```

then import the package in your project
```
import { TempButton } from "rs-ui-react-components";
```
and use it in your project
```
<TempButton variant="primary" size="medium" onClick={() => console.log("clicked")}>
  Click me
</TempButton>
```
## Pushing features and fixes
When a change is pushed the verison in package.json needs to be updated.
Due to that this package is published as an Azure artifact.
If not version is updated, the pipeline will fail.