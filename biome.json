{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/dist", "!**/coverage"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineEnding": "lf"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}, "includes": ["**", "!**/dist/**", "!**/coverage/**"]}, "javascript": {"formatter": {"quoteStyle": "double"}}}