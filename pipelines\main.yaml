name: rs-ui-react-components CI Pipeline

# This triggers the pipeline on pushes to main (like after a merge)
trigger:
  branches:
    include:
      - main


variables:
  nodeVersion: '22.x'
  tag: '$(Build.BuildId)'
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: BuildAndTest
        displayName: Build, Test
        timeoutInMinutes: 10

        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              pnpm_config_cache: $(pnpm_config_cache)

          - template: templates/test.yaml
          
          - template: templates/build.yaml

          - template: templates/publish.yaml
            parameters:
              artifactName: 'rs-ui-react-components'