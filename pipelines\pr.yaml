# This triggers the pipeline on Pull Requests targeting main
name: rs-ui-react-components PR Pipeline

pr:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '22.x'

stages:
  - stage: Build
    displayName: Build and Test
    jobs:
      - job: BuildAndTest
        displayName: Build, Test
        timeoutInMinutes: 10
        steps:
          # Verify version is bumped
          - template: templates/version-check.yaml

          - template: templates/setup.yaml
            parameters:
              nodeVersion: $(nodeVersion)
              
          - template: templates/test.yaml

          - template: templates/build.yaml
            parameters:
              buildArguments: ''
