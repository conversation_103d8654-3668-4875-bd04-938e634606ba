# Next.js Security Scanning
# Runs security scans on the Next.js codebase
name: Next.js FHIR Client Security Scanning

trigger:
  branches:
    exclude:
      - '*'

schedules:
  - cron: "0 0 * * *" # Runs daily at midnight UTC
    displayName: "Midnight Build"
    branches:
      include:
        - main
    always: true

  - cron: "0 12 * * *" # Runs daily at midday UTC
    displayName: "Midday Build"
    branches:
      include:
        - main
    always: true

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '22.x'

steps:
  # Setup security scanning
  - task: AdvancedSecurity-Codeql-Init@1
    inputs:
      languages: "javascript"
      enableAutomaticCodeQLInstall: true

  - template: templates/setup.yaml
    parameters:
      nodeVersion: $(nodeVersion)

  - template: templates/build.yaml
    parameters:
      buildArguments: ''

  # Security Scanning
  - task: AdvancedSecurity-Dependency-Scanning@1
  - task: AdvancedSecurity-Codeql-Analyze@1

  # Additional npm audit
  - script: |
      pnpm audit
    displayName: 'Run pnpm audit'
    continueOnError: true
