import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import GhostButton from "./GhostButton";

const meta: Meta<typeof GhostButton> = {
	title: "UI/Ghost Button",
	component: GhostButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		hasIcon: {
			control: { type: "boolean" },
			description: "Show leading icon",
		},
		hasTrailingIcon: {
			control: { type: "boolean" },
			description: "Show trailing icon",
		},
		selected: {
			control: { type: "boolean" },
			description: "Selected state",
		},
		disabled: {
			control: { type: "boolean" },
			description: "Disabled state",
		},
		children: {
			control: { type: "text" },
			description: "Button label",
		},
	},
};

export default meta;
type Story = StoryObj<typeof GhostButton>;

export const Default: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
};

export const Hover: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
	parameters: {
		pseudo: { hover: true },
	},
};

export const Focus: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
	parameters: {
		pseudo: { focus: true },
	},
};

export const HoverAndFocus: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
	parameters: {
		pseudo: { hover: true, focus: true },
	},
};

export const Active: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
	parameters: {
		pseudo: { active: true },
	},
};

export const ActiveAndFocus: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
	},
	parameters: {
		pseudo: { active: true, focus: true },
	},
};

export const Selected: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: true,
	},
};

export const SelectedAndFocus: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: true,
	},
	parameters: {
		pseudo: { focus: true },
	},
};

export const SelectedAndHover: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: true,
	},
	parameters: {
		pseudo: { hover: true },
	},
};

export const WithoutIcon: Story = {
	args: {
		children: "Label",
		hasIcon: false,
		hasTrailingIcon: true,
		selected: false,
	},
};

export const WithoutTrailingIcon: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: false,
		selected: false,
	},
};

export const TextOnly: Story = {
	args: {
		children: "Label",
		hasIcon: false,
		hasTrailingIcon: false,
		selected: false,
	},
};

export const Disabled: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
		disabled: true,
	},
};

export const DisabledSelected: Story = {
	args: {
		children: "Label",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: true,
		disabled: true,
	},
};

export const CustomIcons: Story = {
	args: {
		children: "Custom",
		hasIcon: true,
		hasTrailingIcon: true,
		selected: false,
		icon: (
			<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
				<title>Custom icon</title>
				<path
					d="M12 2L2 7L12 12L22 7L12 2Z"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
				<path
					d="M2 17L12 22L22 17"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
				<path
					d="M2 12L12 17L22 12"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
		),
		trailingIcon: (
			<svg width="24" height="24" viewBox="0 0 24 24" fill="none">
				<title>trailingIcon</title>
				<path
					d="M5 12H19"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
				<path
					d="M12 5L19 12L12 19"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
				/>
			</svg>
		),
	},
};
