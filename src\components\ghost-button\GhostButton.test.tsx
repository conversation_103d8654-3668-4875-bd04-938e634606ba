import { fireEvent, render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { GhostButton } from "./GhostButton";

describe("GhostButton", () => {
	it("renders correctly with default props", () => {
		render(<GhostButton>Label</GhostButton>);
		expect(screen.getByText("Label")).toBeInTheDocument();
		expect(screen.getByRole("button")).toBeInTheDocument();
	});

	it("calls onClick when clicked", () => {
		const handleClick = vi.fn();
		render(<GhostButton onClick={handleClick}>Click me</GhostButton>);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).toHaveBeenCalledTimes(1);
	});

	it("does not call onClick when disabled", () => {
		const handleClick = vi.fn();
		render(
			<GhostButton onClick={handleClick} disabled={true}>
				Click me
			</GhostButton>,
		);
		fireEvent.click(screen.getByText("Click me"));
		expect(handleClick).not.toHaveBeenCalled();
	});

	it("renders with leading icon by default", () => {
		render(<GhostButton>Label</GhostButton>);
		const button = screen.getByRole("button");
		const svgs = button.querySelectorAll("svg");
		expect(svgs.length).toBeGreaterThan(0);
	});

	it("renders with trailing icon by default", () => {
		render(<GhostButton>Label</GhostButton>);
		const button = screen.getByRole("button");
		const svgs = button.querySelectorAll("svg");
		expect(svgs.length).toBe(2); // Leading + trailing icons
	});

	it("hides leading icon when hasIcon is false", () => {
		render(<GhostButton hasIcon={false}>Label</GhostButton>);
		const button = screen.getByRole("button");
		const svgs = button.querySelectorAll("svg");
		expect(svgs.length).toBe(1); // Only trailing icon
	});

	it("hides trailing icon when hasTrailingIcon is false", () => {
		render(<GhostButton hasTrailingIcon={false}>Label</GhostButton>);
		const button = screen.getByRole("button");
		const svgs = button.querySelectorAll("svg");
		expect(svgs.length).toBe(1); // Only leading icon
	});

	it("renders without icons when both are disabled", () => {
		render(
			<GhostButton hasIcon={false} hasTrailingIcon={false}>
				Label
			</GhostButton>,
		);
		const button = screen.getByRole("button");
		const svgs = button.querySelectorAll("svg");
		expect(svgs.length).toBe(0);
	});

	it("applies selected state correctly", () => {
		render(<GhostButton selected={true}>Selected</GhostButton>);
		const button = screen.getByRole("button");
		expect(button).toHaveClass("bg-ct-ghost-button-bg--selected");
	});

	it("applies disabled state correctly", () => {
		render(<GhostButton disabled={true}>Disabled</GhostButton>);
		const button = screen.getByRole("button");
		expect(button).toBeDisabled();
		expect(button).toHaveClass("disabled:opacity-60");
	});

	it("renders custom icon when provided", () => {
		const customIcon = <div data-testid="custom-icon">Custom</div>;
		render(<GhostButton icon={customIcon}>Label</GhostButton>);
		expect(screen.getByTestId("custom-icon")).toBeInTheDocument();
	});

	it("renders custom trailing icon when provided", () => {
		const customTrailingIcon = (
			<div data-testid="custom-trailing-icon">Custom</div>
		);
		render(<GhostButton trailingIcon={customTrailingIcon}>Label</GhostButton>);
		expect(screen.getByTestId("custom-trailing-icon")).toBeInTheDocument();
	});

	it("applies focus styles when focused", () => {
		render(<GhostButton>Label</GhostButton>);
		const button = screen.getByRole("button");
		button.focus();
		expect(button).toHaveClass("focus:ring-2");
		expect(button).toHaveClass("focus:ring-ct-focus");
	});

	it("applies base button styling", () => {
		render(<GhostButton>Label</GhostButton>);
		const button = screen.getByRole("button");
		expect(button).toHaveClass("inline-flex");
		expect(button).toHaveClass("rounded-3xl");
		expect(button).toHaveClass("px-6");
		expect(button).toHaveClass("py-3");
	});

	it("supports all standard button attributes", () => {
		render(
			<GhostButton
				type="submit"
				name="test-button"
				value="test-value"
				data-testid="ghost-button"
			>
				Label
			</GhostButton>,
		);
		const button = screen.getByTestId("ghost-button");
		expect(button).toHaveAttribute("type", "submit");
		expect(button).toHaveAttribute("name", "test-button");
		expect(button).toHaveAttribute("value", "test-value");
	});
});
