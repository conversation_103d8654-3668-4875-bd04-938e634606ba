import type { ButtonHTMLAttributes, ReactNode } from "react";

export interface GhostButtonProps
	extends ButtonHTMLAttributes<HTMLButtonElement> {
	/**
	 * Button label text
	 */
	children: ReactNode;
	/**
	 * Whether to show the leading icon
	 */
	hasIcon?: boolean;
	/**
	 * Whether to show the trailing icon
	 */
	hasTrailingIcon?: boolean;
	/**
	 * Whether the button is selected
	 */
	selected?: boolean;
	/**
	 * Custom leading icon element
	 */
	icon?: ReactNode;
	/**
	 * Custom trailing icon element
	 */
	trailingIcon?: ReactNode;
	/**
	 * Optional click handler
	 */
	onClick?: () => void;
}

const FileIcon = () => (
	<svg
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<title>FileIcon</title>
		<path
			d="M9 2.00318V2H19.9978C20.5513 2 21 2.45531 21 2.9918V21.0082C21 21.556 20.5551 22 20.0066 22H3.9934C3.44476 22 3 21.5501 3 20.9932V8L9 2.00318ZM5.82918 8H9V4.83086L5.82918 8ZM11 4V9C11 9.55228 10.5523 10 10 10H5V20H19V4H11Z"
			fill="currentColor"
		/>
	</svg>
);

const DropdownIcon = () => (
	<svg
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<title>DropdownIcon</title>
		<path
			d="M12 15.0006L7.75732 10.758L9.17154 9.34375L12 12.1722L14.8284 9.34375L16.2426 10.758L12 15.0006Z"
			fill="currentColor"
		/>
	</svg>
);

/**
 * Ghost button component with transparent background and multiple states
 */
export const GhostButton = ({
	children,
	hasIcon = true,
	hasTrailingIcon = true,
	selected = false,
	icon,
	trailingIcon,
	disabled = false,
	...props
}: GhostButtonProps) => {
	return (
		<button
			type="button"
			className={`
				inline-flex 
				items-center 
				justify-center
				gap-4 
				px-6 
				py-3 
				rounded-3xl 
				text-base 
				font-bold 
				leading-6 
				text-ct-foreground
				font-sans
				transition-all 
				duration-150 
				ease-in-out
				relative
				focus:outline-none
				focus:ring-2
				focus:ring-ct-focus
				focus:ring-offset-2
				disabled:opacity-60
				disabled:cursor-not-allowed
				${
					selected
						? "bg-ct-ghost-button-bg--selected hover:[&:not(:disabled)]:before:absolute hover:[&:not(:disabled)]:before:inset-0 hover:[&:not(:disabled)]:before:bg-ct-ghost-button-bg--hover hover:[&:not(:disabled)]:before:rounded-3xl hover:[&:not(:disabled)]:before:content-[''] active:[&:not(:disabled)]:before:bg-ct-ghost-button-bg--active"
						: "bg-ct-ghost-button-bg hover:[&:not(:disabled)]:bg-ct-ghost-button-bg--hover active:[&:not(:disabled)]:bg-ct-ghost-button-bg--active"
				}
			`}
			disabled={disabled}
			{...props}
		>
			{hasIcon && (
				<div className="flex items-center justify-center w-6 h-6 flex-shrink-0 relative z-10">
					{icon || <FileIcon />}
				</div>
			)}

			<div className="flex flex-col justify-center items-start relative z-10">
				<span className="font-bold text-base leading-6 text-ct-foreground whitespace-nowrap">
					{children}
				</span>
			</div>

			{hasTrailingIcon && (
				<div className="flex items-center justify-center w-6 h-6 flex-shrink-0 relative z-10">
					{trailingIcon || <DropdownIcon />}
				</div>
			)}
		</button>
	);
};

export default GhostButton;
