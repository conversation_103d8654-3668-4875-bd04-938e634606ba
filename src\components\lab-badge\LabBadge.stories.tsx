import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { LabBadge } from "./LabBadge";

const meta: Meta<typeof LabBadge> = {
	title: "UI/Lab Badge",
	component: LabBadge,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		children: {
			control: "text",
			description: "The text content of the badge",
		},
		dismissible: {
			control: "boolean",
			description: "Whether to show the close button",
		},
		variant: {
			control: "select",
			options: ["default", "info", "success", "warning", "error"],
			description: "Badge styling variant",
		},
		size: {
			control: "select",
			options: ["small", "medium", "large"],
			description: "Badge size",
		},
		onDismiss: {
			action: "dismissed",
			description: "Callback fired when the close button is clicked",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: "Hematologi, Inflammation",
		dismissible: true,
	},
};

export const NonDismissible: Story = {
	args: {
		children: "Label",
		dismissible: false,
	},
};

export const Variants: Story = {
	render: () => (
		<div className="flex flex-wrap gap-4">
			<LabBadge variant="default" dismissible>
				Default
			</LabBadge>
			<LabBadge variant="info" dismissible>
				Info
			</LabBadge>
			<LabBadge variant="success" dismissible>
				Success
			</LabBadge>
			<LabBadge variant="warning" dismissible>
				Warning
			</LabBadge>
			<LabBadge variant="error" dismissible>
				Error
			</LabBadge>
		</div>
	),
};

export const Sizes: Story = {
	render: () => (
		<div className="flex flex-wrap items-center gap-4">
			<LabBadge size="small" dismissible>
				Small Badge
			</LabBadge>
			<LabBadge size="medium" dismissible>
				Medium Badge
			</LabBadge>
			<LabBadge size="large" dismissible>
				Large Badge
			</LabBadge>
		</div>
	),
};

export const LongText: Story = {
	args: {
		children:
			"This is a very long badge text that should handle wrapping gracefully",
		dismissible: true,
	},
};

export const Interactive: Story = {
	render: () => {
		const handleDismiss = () => {
			alert("Badge dismissed!");
		};

		return (
			<div className="flex flex-wrap gap-4">
				<LabBadge dismissible onDismiss={handleDismiss}>
					Click X to dismiss
				</LabBadge>
				<LabBadge variant="info" dismissible onDismiss={handleDismiss}>
					Info Badge
				</LabBadge>
			</div>
		);
	},
};
