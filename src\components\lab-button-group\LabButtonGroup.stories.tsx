import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useState } from "react";
import { LabButtonGroup } from "./LabButtonGroup";

const meta: Meta<typeof LabButtonGroup> = {
	title: "UI/Lab ButtonGroup",
	component: LabButtonGroup,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		onChange: { action: "changed" },
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

// Template component for interactive stories
// biome-ignore lint/suspicious/noExplicitAny: Only used for testing
const InteractiveTemplate = (args: any) => {
	const [selectedId, setSelectedId] = useState(args.selectedId);

	return (
		<LabButtonGroup
			{...args}
			selectedId={selectedId}
			onChange={(id) => {
				setSelectedId(id);
				args.onChange?.(id);
			}}
		/>
	);
};

export const Default: Story = {
	args: {
		options: [
			{ id: "1", label: "1 år" },
			{ id: "3", label: "3 år" },
			{ id: "5", label: "5 år" },
			{ id: "max", label: "Max" },
		],
		selectedId: "3",
		name: "payment-period",
	},
	render: InteractiveTemplate,
};

export const WithoutSelection: Story = {
	args: {
		options: [
			{ id: "1", label: "1 år" },
			{ id: "3", label: "3 år" },
			{ id: "5", label: "5 år" },
			{ id: "max", label: "Max" },
		],
		name: "payment-period",
	},
	render: InteractiveTemplate,
};

export const TwoOptions: Story = {
	args: {
		options: [
			{ id: "yes", label: "Yes" },
			{ id: "no", label: "No" },
		],
		selectedId: "yes",
		name: "confirmation",
	},
	render: InteractiveTemplate,
};

export const FiveOptions: Story = {
	args: {
		options: [
			{ id: "xs", label: "XS" },
			{ id: "s", label: "S" },
			{ id: "m", label: "M" },
			{ id: "l", label: "L" },
			{ id: "xl", label: "XL" },
		],
		selectedId: "m",
		name: "size-selection",
	},
	render: InteractiveTemplate,
};

export const WithDisabledOption: Story = {
	args: {
		options: [
			{ id: "1", label: "1 år" },
			{ id: "3", label: "3 år", disabled: true },
			{ id: "5", label: "5 år" },
			{ id: "max", label: "Max" },
		],
		selectedId: "1",
		name: "payment-period",
	},
	render: InteractiveTemplate,
};

export const LongLabels: Story = {
	args: {
		options: [
			{ id: "short", label: "Short term" },
			{ id: "medium", label: "Medium term" },
			{ id: "long", label: "Long term" },
			{ id: "permanent", label: "Permanent" },
		],
		selectedId: "medium",
		name: "duration",
	},
	render: InteractiveTemplate,
};
