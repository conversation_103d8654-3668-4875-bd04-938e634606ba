import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import TableCellDate from "./TableCellDate";

describe("TableCellDate", () => {
	const mockDateTime = "2025-03-01 12:33";

	it("renders with default props", () => {
		render(<TableCellDate dateTime={mockDateTime} />);

		const cell = screen.getByRole("columnheader");
		expect(cell).toBeInTheDocument();
		expect(cell).toHaveTextContent("2025-03-01 12:33");
	});

	it("renders as th when type is th", () => {
		render(<TableCellDate dateTime={mockDateTime} type="th" />);

		const cell = screen.getByRole("columnheader");
		expect(cell).toBeInTheDocument();
		expect(cell.tagName).toBe("TH");
	});

	it("renders as td when type is td", () => {
		render(
			<table>
				<tbody>
					<tr>
						<TableCellDate dateTime={mockDateTime} type="td" />
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toBeInTheDocument();
		expect(cell.tagName).toBe("TD");
	});

	it("formats date and time correctly", () => {
		render(<TableCellDate dateTime={mockDateTime} />);

		const cell = screen.getByRole("columnheader");
		const dateSpan = cell.querySelector("span:first-child");
		const timeSpan = cell.querySelector("span:last-child");

		expect(dateSpan).toHaveTextContent("2025-03-01");
		expect(dateSpan).toHaveClass("font-bold");
		expect(timeSpan).toHaveTextContent("12:33");
		expect(timeSpan).toHaveClass("font-normal");
	});

	it("applies correct alignment classes", () => {
		const { rerender } = render(
			<TableCellDate dateTime={mockDateTime} textAlign="left" />,
		);

		let cell = screen.getByRole("columnheader");
		let firstDiv = cell.firstChild;
		expect(firstDiv).toHaveClass("items-start");

		rerender(<TableCellDate dateTime={mockDateTime} textAlign="center" />);
		cell = screen.getByRole("columnheader");
		firstDiv = cell.firstChild;
		expect(firstDiv).toHaveClass("items-center");

		rerender(<TableCellDate dateTime={mockDateTime} textAlign="right" />);
		cell = screen.getByRole("columnheader");
		firstDiv = cell.firstChild;
		expect(firstDiv).toHaveClass("items-end");
	});

	it("applies custom className", () => {
		render(<TableCellDate dateTime={mockDateTime} className="custom-class" />);

		const cell = screen.getByRole("columnheader");
		expect(cell).toHaveClass("custom-class");
	});

	it("applies correct border classes for th", () => {
		render(<TableCellDate dateTime={mockDateTime} type="th" />);

		const cell = screen.getByRole("columnheader");
		expect(cell).toHaveClass(
			"border-t-2",
			"border-t-ct-foreground",
			"border-b",
			"border-ct-foreground",
		);
	});

	it("applies correct border classes for td", () => {
		render(
			<table>
				<tbody>
					<tr>
						<TableCellDate dateTime={mockDateTime} type="td" />
					</tr>
				</tbody>
			</table>,
		);

		const cell = screen.getByRole("cell");
		expect(cell).toHaveClass("border-b", "border-ct-foreground");
		expect(cell).not.toHaveClass("border-t-2");
	});

	it("passes through additional props", () => {
		render(<TableCellDate dateTime={mockDateTime} data-testid="custom-cell" />);

		const cell = screen.getByTestId("custom-cell");
		expect(cell).toBeInTheDocument();
	});
});
