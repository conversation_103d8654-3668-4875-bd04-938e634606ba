import type { <PERSON>a, StoryObj } from "@storybook/react";
import { LabTableColumnHeader } from "./LabTableColumnHeader";

const meta: Meta<typeof LabTableColumnHeader> = {
	title: "UI/Lab Table Column Header",
	component: LabTableColumnHeader,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		children: {
			control: "text",
			description: "The content of the table cell",
		},
		type: {
			control: "select",
			options: ["th", "td"],
			description: "Type of table cell",
		},
		textAlign: {
			control: "select",
			options: ["left", "center", "right"],
			description: "Text alignment within the cell",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const ColumnHeader: Story = {
	args: {
		children: "Analys",
		type: "th",
		textAlign: "left",
	},
};

export const ColumnHeaderData: Story = {
	args: {
		children: "Sample data content",
		type: "td",
		textAlign: "left",
	},
};

export const TextAlignments: Story = {
	render: () => (
		<table className="border-collapse">
			<thead>
				<tr>
					<LabTableColumnHeader type="th" textAlign="left">
						Left Aligned
					</LabTableColumnHeader>
					<LabTableColumnHeader type="th" textAlign="center">
						Center Aligned
					</LabTableColumnHeader>
					<LabTableColumnHeader type="th" textAlign="right">
						Right Aligned
					</LabTableColumnHeader>
				</tr>
			</thead>
			<tbody>
				<tr>
					<LabTableColumnHeader type="td" textAlign="left">
						Data left
					</LabTableColumnHeader>
					<LabTableColumnHeader type="td" textAlign="center">
						Data center
					</LabTableColumnHeader>
					<LabTableColumnHeader type="td" textAlign="right">
						Data right
					</LabTableColumnHeader>
				</tr>
			</tbody>
		</table>
	),
};

export const CompleteTable: Story = {
	render: () => (
		<div className="w-full max-w-4xl">
			<table className="w-full border-collapse">
				<thead>
					<tr>
						<LabTableColumnHeader type="th" textAlign="left">
							Patient ID
						</LabTableColumnHeader>
						<LabTableColumnHeader type="th" textAlign="left">
							Analys
						</LabTableColumnHeader>
						<LabTableColumnHeader type="th" textAlign="center">
							Result
						</LabTableColumnHeader>
						<LabTableColumnHeader type="th" textAlign="right">
							Date
						</LabTableColumnHeader>
					</tr>
				</thead>
				<tbody>
					<tr>
						<LabTableColumnHeader type="td" textAlign="left">
							12345
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="left">
							Blood Test
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="center">
							Normal
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="right">
							2024-01-15
						</LabTableColumnHeader>
					</tr>
					<tr>
						<LabTableColumnHeader type="td" textAlign="left">
							12346
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="left">
							X-Ray
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="center">
							Abnormal
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="right">
							2024-01-16
						</LabTableColumnHeader>
					</tr>
					<tr>
						<LabTableColumnHeader type="td" textAlign="left">
							12347
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="left">
							MRI Scan
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="center">
							Normal
						</LabTableColumnHeader>
						<LabTableColumnHeader type="td" textAlign="right">
							2024-01-17
						</LabTableColumnHeader>
					</tr>
				</tbody>
			</table>
		</div>
	),
};

export const LongContent: Story = {
	args: {
		children:
			"This is a very long content that might wrap to multiple lines in the table cell and should handle gracefully",
		type: "td",
		textAlign: "left",
	},
};
