import type { HTMLAttributes, ReactNode } from "react";

export interface LabTableRowHeaderProps
	extends Omit<HTMLAttributes<HTMLTableCellElement>, "children"> {
	/**
	 * Header content
	 */
	children: ReactNode;
	/**
	 * Text alignment within the header
	 */
	textAlign?: "left" | "center" | "right";
	/**
	 * Additional CSS classes
	 */
	className?: string;
}

/**
 * Table row header component specifically designed for table headers with emphasized bottom border
 */
export const LabTableRowHeader = ({
	children,
	textAlign = "left",
	className = "",
	...props
}: LabTableRowHeaderProps) => {
	const baseClasses =
		"inline-flex h-16 px-4 py-3 flex-col justify-end flex-shrink-0";

	const borderClasses = "border-b-4 border-ct-foreground";

	const alignmentClasses = {
		left: "items-start text-left",
		center: "items-center text-center",
		right: "items-end text-right",
	};

	return (
		<th
			className={`
				${baseClasses}
				${borderClasses}
				${alignmentClasses[textAlign]}
				${className}
			`
				.trim()
				.replace(/\s+/g, " ")}
			{...props}
		>
			<div className="self-stretch text-ct-foreground font-sans text-base font-bold leading-6">
				<span className="font-sans font-bold text-base text-ct-foreground">
					{children}
				</span>
			</div>
		</th>
	);
};

export default LabTableRowHeader;
