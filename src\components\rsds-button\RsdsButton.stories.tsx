import type { Meta, StoryObj } from "@storybook/react-vite";
import RsdsButton from "./RsdsButton";

const meta: Meta<typeof RsdsButton> = {
	title: "UI/Rsds Button",
	component: RsdsButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["main", "default"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof RsdsButton>;

export const Main: Story = {
	args: {
		variant: "main",
		form: "rounded",
		children: "Button",
	},
};
export const Disabled: Story = {
	args: {
		variant: "main",
		form: "rounded",
		children: "Button",
		disabled: true,
	},
};

export const Default: Story = {
	args: {
		variant: "default",
		children: "Button",
	},
};

export const Square: Story = {
	args: {
		children: "Square Button",
		form: "square",
	},
};
