import react from "@vitejs/plugin-react";
import { defineConfig } from "vitest/config";

export default defineConfig({
	plugins: [react()],
	test: {
		environment: "jsdom",
		globals: true,
		setupFiles: ["./vitest.setup.ts"],
		reporters: ["default", "junit"],
		outputFile: {
			junit: "./junit.xml",
		},
		coverage: {
			provider: "v8",
			reporter: ["text", "json", "html", "cobertura"],
			reportsDirectory: "./coverage",
		},
	},
});
